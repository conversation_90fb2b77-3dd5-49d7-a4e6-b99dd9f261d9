<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace::css/style.css"/>
        <css src="Webkul_Marketplace::css/product.css"/>
        <css src="Webkul_Marketplace::css/layout.css"/>
    </head>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Manage Shipping Methods</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
            <block class="Coditron\CustomShippingRate\Block\ShippingTabs" name="sellership_shipping_tabs" template="Coditron_CustomShippingRate::shipping/tabs.phtml" cacheable="false">
                <block class="Magento\Framework\View\Element\Template" name="shipping_methods_tab" template="Coditron_CustomShippingRate::shiprate/list.phtml" cacheable="false">
                    <container name="shipping_methods_grid">
                        <uiComponent name="sellership_rates_list_front"/>
                    </container>
                </block>
                <block class="Coditron\CustomShippingRate\Block\FreeShippingThresholds" name="free_shipping_thresholds_tab" template="Coditron_CustomShippingRate::shipping/thresholds.phtml" cacheable="false">
                    <container name="free_shipping_thresholds_grid">
                        <uiComponent name="sellership_free_shipping_thresholds_list"/>
                    </container>
                </block>
            </block>
        </referenceContainer>
    </body>
</page>
