<?php
/**
 * @var \Coditron\CustomShippingRate\Block\ShippingTabs $block
 * @var \Magento\Framework\Escaper $escaper
 */
?>
<div class="wk-mp-shipping-tabs-container">
    <!-- Tab Navigation -->
    <div class="wk-mp-tabs-nav">
        <ul class="wk-mp-tabs-list">
            <li class="wk-mp-tab-item <?= $block->isTabActive('shipping_methods') ? 'wk-mp-active' : '' ?>">
                <a href="<?= $escaper->escapeUrl($block->getTabUrl('shipping_methods')) ?>" 
                   class="wk-mp-tab-link">
                    <?= $escaper->escapeHtml(__('Shipping Methods')) ?>
                </a>
            </li>
            <li class="wk-mp-tab-item <?= $block->isTabActive('free_shipping_thresholds') ? 'wk-mp-active' : '' ?>">
                <a href="<?= $escaper->escapeUrl($block->getTabUrl('free_shipping_thresholds')) ?>" 
                   class="wk-mp-tab-link">
                    <?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?>
                </a>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="wk-mp-tabs-content">
        <?php if ($block->getCurrentTab() === 'shipping_methods'): ?>
            <div class="wk-mp-tab-content wk-mp-active" id="shipping-methods-tab">
                <?= /* @noEscape */ $block->getShippingMethodsTabContent() ?>
            </div>
        <?php elseif ($block->getCurrentTab() === 'free_shipping_thresholds'): ?>
            <div class="wk-mp-tab-content wk-mp-active" id="free-shipping-thresholds-tab">
                <?= /* @noEscape */ $block->getFreeShippingThresholdsTabContent() ?>
            </div>
        <?php else: ?>
            <!-- Default to shipping methods tab -->
            <div class="wk-mp-tab-content wk-mp-active" id="shipping-methods-tab">
                <?= /* @noEscape */ $block->getShippingMethodsTabContent() ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.wk-mp-shipping-tabs-container {
    margin: 20px 0;
}

.wk-mp-tabs-nav {
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.wk-mp-tabs-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.wk-mp-tab-item {
    margin-right: 2px;
}

.wk-mp-tab-link {
    display: block;
    padding: 12px 20px;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-bottom: none;
    text-decoration: none;
    color: #333;
    border-radius: 4px 4px 0 0;
}

.wk-mp-tab-item.wk-mp-active .wk-mp-tab-link {
    background: #fff;
    color: #007bdb;
    font-weight: bold;
}

.wk-mp-tab-link:hover {
    background: #e9e9e9;
    text-decoration: none;
}

.wk-mp-tab-item.wk-mp-active .wk-mp-tab-link:hover {
    background: #fff;
}

.wk-mp-tabs-content {
    min-height: 400px;
}

.wk-mp-tab-content {
    display: none;
}

.wk-mp-tab-content.wk-mp-active {
    display: block;
}
</style>
