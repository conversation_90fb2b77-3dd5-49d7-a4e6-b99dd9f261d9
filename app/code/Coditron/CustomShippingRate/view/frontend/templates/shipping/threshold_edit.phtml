<?php
/**
 * @var \Coditron\CustomShippingRate\Block\FreeShippingThresholdEdit $block
 * @var \Magento\Framework\Escaper $escaper
 */
$thresholdData = $block->getThresholdData();
$isEditMode = $block->isEditMode();
?>
<div class="wk-mp-free-shipping-threshold-edit-container">
    <div class="page-main-actions">
        <div class="page-actions-placeholder"></div>
        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
            <div class="page-actions-inner">
                <div class="page-actions-buttons">
                    <button type="button" class="action- scalable back wk-ui-grid-btn"
                            onclick="location.href='<?= $escaper->escapeUrl($block->getBackUrl()) ?>';"
                            data-ui-id="back-button">
                        <span><?= $escaper->escapeHtml(__('Back')) ?></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <form class="form" id="free-shipping-threshold-form" action="<?= $escaper->escapeUrl($block->getSaveUrl()) ?>" method="post" data-hasrequired="<?= $escaper->escapeHtml(__('* Required Fields')) ?>">
        <?= $block->getBlockHtml('formkey') ?>

        <input type="hidden" name="id" value="<?= $escaper->escapeHtml($thresholdData->getShiptableratesId()) ?>" />
        <input type="hidden" name="type" value="threshold" />
        
        <fieldset class="fieldset wk-mp-fieldset">
            <legend class="legend"><span><?= $escaper->escapeHtml($isEditMode ? __('Edit Free Shipping Threshold') : __('Add New Free Shipping Threshold')) ?></span></legend>
            
            <div class="field required">
                <label for="courier_name" class="label"><span><?= $escaper->escapeHtml(__('Courier Name')) ?></span></label>
                <div class="control">
                    <input type="text" name="courier_name" id="courier_name" class="input-text required-entry" 
                           value="<?= $escaper->escapeHtml($thresholdData->getCourierName()) ?>" />
                </div>
            </div>

            <div class="field required">
                <label for="service_type" class="label"><span><?= $escaper->escapeHtml(__('Service Type')) ?></span></label>
                <div class="control">
                    <input type="text" name="service_type" id="service_type" class="input-text required-entry" 
                           value="<?= $escaper->escapeHtml($thresholdData->getServiceType()) ?>" />
                </div>
            </div>

            <div class="field required">
                <label for="countries" class="label"><span><?= $escaper->escapeHtml(__('Countries')) ?></span></label>
                <div class="control">
                    <select name="countries[]" id="countries" class="select multiselect required-entry" multiple="multiple">
                        <?php
                        $selectedCountries = $thresholdData->getCountries();
                        $countries = $block->getDirectoryBlock()->getCountryCollection()->toOptionArray();
                        foreach ($countries as $country):
                            if (empty($country['value'])) continue;
                            $selected = in_array($country['value'], $selectedCountries) ? 'selected="selected"' : '';
                        ?>
                            <option value="<?= $escaper->escapeHtml($country['value']) ?>" <?= $selected ?>>
                                <?= $escaper->escapeHtml($country['label']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="field required">
                <label for="min_amount" class="label"><span><?= $escaper->escapeHtml(__('Minimum Amount for Free Shipping')) ?></span></label>
                <div class="control">
                    <input type="number" name="min_amount" id="min_amount" class="input-text required-entry validate-greater-than-zero" 
                           step="0.01" min="0.01" value="<?= $escaper->escapeHtml($thresholdData->getMinAmount()) ?>" />
                    <div class="note"><?= $escaper->escapeHtml(__('Orders above this amount will qualify for free shipping.')) ?></div>
                </div>
            </div>

            <div class="field">
                <label for="weight" class="label"><span><?= $escaper->escapeHtml(__('Maximum Weight (kg)')) ?></span></label>
                <div class="control">
                    <input type="text" name="weight" id="weight" class="input-text" 
                           value="<?= $escaper->escapeHtml($thresholdData->getWeight()) ?>" />
                    <div class="note"><?= $escaper->escapeHtml(__('Leave empty for no weight limit.')) ?></div>
                </div>
            </div>

            <div class="field">
                <label for="packing_time" class="label"><span><?= $escaper->escapeHtml(__('Packing Time (days)')) ?></span></label>
                <div class="control">
                    <input type="number" name="packing_time" id="packing_time" class="input-text" 
                           min="0" value="<?= $escaper->escapeHtml($thresholdData->getPackingTime()) ?>" />
                </div>
            </div>

            <div class="field">
                <label for="delivery_time" class="label"><span><?= $escaper->escapeHtml(__('Delivery Time (days)')) ?></span></label>
                <div class="control">
                    <input type="number" name="delivery_time" id="delivery_time" class="input-text" 
                           min="0" value="<?= $escaper->escapeHtml($thresholdData->getDeliveryTime()) ?>" />
                </div>
            </div>
        </fieldset>

        <div class="actions-toolbar">
            <div class="primary">
                <button type="submit" class="action submit primary wk-ui-grid-btn wk-ui-grid-btn-primary" title="<?= $escaper->escapeHtml(__('Save')) ?>">
                    <span><?= $escaper->escapeHtml(__('Save')) ?></span>
                </button>
            </div>
            <div class="secondary">
                <button type="button" class="action back wk-ui-grid-btn" 
                        onclick="location.href='<?= $escaper->escapeUrl($block->getBackUrl()) ?>';" 
                        title="<?= $escaper->escapeHtml(__('Back')) ?>">
                    <span><?= $escaper->escapeHtml(__('Back')) ?></span>
                </button>
            </div>
        </div>
    </form>
</div>

<script type="text/x-magento-init">
{
    "#free-shipping-threshold-form": {
        "validation": {}
    }
}
</script>
