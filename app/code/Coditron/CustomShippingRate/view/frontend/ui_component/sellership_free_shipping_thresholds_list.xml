<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">sellership_free_shipping_thresholds_list.sellership_free_shipping_thresholds_list_data_source</item>
            <item name="deps" xsi:type="string">sellership_free_shipping_thresholds_list.sellership_free_shipping_thresholds_list_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">sellership_free_shipping_thresholds_columns</item>
    </argument>
    <dataSource name="sellership_free_shipping_thresholds_list_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Coditron\CustomShippingRate\Ui\DataProvider\FreeShippingThresholdsDataProvider</argument>
            <argument name="name" xsi:type="string">sellership_free_shipping_thresholds_list_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">shiptablerates_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="marketplace/mui_index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="cacheRequests" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="statefull" xsi:type="array">
                        <item name="applied" xsi:type="boolean">false</item>
                    </item>
                    <item name="params" xsi:type="array">
                        <item name="filters_modifier" xsi:type="array" />
                    </item>
                </item>
            </argument>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">sellership_free_shipping_thresholds_list.sellership_free_shipping_thresholds_list.sellership_free_shipping_thresholds_columns.ids</item>
                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
                </item>
            </argument>
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="coditron_customshippingrate/freeshippingthreshold/massDelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected free shipping thresholds?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="sellership_free_shipping_thresholds_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="childDefaults" xsi:type="array">
                    <item name="fieldAction" xsi:type="array">
                        <item name="provider" xsi:type="string">freeShippingThresholdGrid</item>
                        <item name="target" xsi:type="string">selectFreeShippingThreshold</item>
                        <item name="params" xsi:type="array">
                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        </item>
                    </item>
                </item>
            </item>
        </argument>
        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
                    <item name="sortOrder" xsi:type="number">0</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="shiptablerates_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="sorting" xsi:type="string">asc</item>
                    <item name="label" xsi:type="string" translate="true">Id</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="fieldClass" xsi:type="string">wk-mpsr-cl-id-col</item>
                </item>
            </argument>
        </column>
        <column name="courier_name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Courier name</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                    <item name="fieldClass" xsi:type="string">wk-mpsr-cl-name-col</item>
                </item>
            </argument>
        </column>
        <column name="service_type">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Service type</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                    <item name="fieldClass" xsi:type="string">wk-mpsr-cl-name-col</item>
                </item>
            </argument>
        </column>
        <column name="countries">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Countries</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                    <item name="fieldClass" xsi:type="string">wk-mpsr-cl-country-col</item>
                </item>
            </argument>
        </column>
        <column name="min_amount">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Minimum Amount</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                    <item name="fieldClass" xsi:type="string">wk-mpsr-cl-amount-col</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="actions" class="Coditron\CustomShippingRate\Ui\Component\Listing\Column\FreeShippingThresholdActions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
                    <item name="viewUrlPath" xsi:type="string">coditron_customshippingrate/freeshippingthreshold/edit</item>
                    <item name="urlEntityParamName" xsi:type="string">id</item>
                    <item name="sortOrder" xsi:type="number">100</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>
