<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\FreeShippingThreshold;

use Magento\Framework\App\Action\HttpGetActionInterface;

class Delete extends \Coditron\CustomShippingRate\Controller\AbstractShiprate implements HttpGetActionInterface
{
    /**
     * Execute action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        try {
            $thresholdId = $this->getRequest()->getParam('id');
            $sellerShiprateCollection = $this->_collectionFactory->create();
            
            if ($thresholdId) {
                $collection = $sellerShiprateCollection->addFieldToFilter('shiptablerates_id', ['in' => $thresholdId])
                                                      ->addFieldToFilter('seller_id', $this->getSellerId())
                                                      ->addFieldToFilter('min_amount', ['gt' => 0]); // Only delete thresholds
            } else {
                $this->messageManager->addError(__('Invalid threshold ID.'));
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/managewithTabs',
                    ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()]
                );
            }
            
            $countRecord = $collection->getSize();
            if ($countRecord > 0) {
                $collection->removeShiprates();
                $this->_helper->clearCache();
                $this->messageManager->addSuccess(
                    __(
                        'A total of %1 free shipping threshold(s) have been deleted.',
                        $countRecord
                    )
                );
            } else {
                $this->messageManager->addError(__('No valid threshold found to delete.'));
            }
        } catch (\Exception $e) {
            $this->messageManager->addError(__('Something went wrong while deleting the threshold.'));
        }

        return $this->resultRedirectFactory->create()->setPath(
            'coditron_customshippingrate/shiptablerates/managewithTabs',
            ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()]
        );
    }
}
