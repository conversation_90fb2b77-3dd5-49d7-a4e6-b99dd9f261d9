<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\FreeShippingThreshold;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;

class NewAction extends \Coditron\CustomShippingRate\Controller\AbstractShiprate implements HttpGetActionInterface
{
    /**
     * Execute Method
     *
     * @return \Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $resultPage = $this->_resultPageFactory->create();
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('sellership_layout2_free_shipping_threshold_edit');
        }

        $resultPage->getConfig()->getTitle()->set(__('Add New Free Shipping Threshold'));
        return $resultPage;
    }
}
