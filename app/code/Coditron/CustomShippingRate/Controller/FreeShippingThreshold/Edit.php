<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\FreeShippingThreshold;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;

class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate implements HttpGetActionInterface
{
    /**
     * Execute Method
     *
     * @return \Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $thresholdId = $this->getRequest()->getParam('id');
        if (!$thresholdId) {
            $this->messageManager->addError(__('Invalid threshold ID.'));
            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/managewithTabs',
                ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()]
            );
        }

        $resultPage = $this->_resultPageFactory->create();
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('sellership_layout2_free_shipping_threshold_edit');
        }

        $resultPage->getConfig()->getTitle()->set(__('Edit Free Shipping Threshold'));
        return $resultPage;
    }
}
