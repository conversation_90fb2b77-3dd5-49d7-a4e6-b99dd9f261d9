<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\FreeShippingThreshold;

use Magento\Framework\App\Action\HttpPostActionInterface;

class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate implements HttpPostActionInterface
{
    /**
     * Save Free Shipping Threshold action
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        if ($this->getRequest()->isPost()) {
            try {
                if (!$this->_formKeyValidator->validate($this->getRequest())) {
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/managewithTabs',
                        ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $postData = $this->getRequest()->getPostValue();
                $postData['seller_id'] = $this->getSellerId();
                
                // Validate that min_amount is provided and greater than 0
                if (!isset($postData['min_amount']) || (float)$postData['min_amount'] <= 0) {
                    $this->messageManager->addError(__('Minimum amount must be greater than 0 for free shipping thresholds.'));
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/managewithTabs',
                        ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()]
                    );
                }

                // Set free_shipping to true for thresholds
                $postData['free_shipping'] = true;
                $postData['shipping_price'] = '0.00'; // Free shipping means 0 price

                $result = $this->_helper->validateData($postData);
                if ($result['error']) {
                    $this->messageManager->addError(__($result['msg']));
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/managewithTabs',
                        ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $sellerShiprate = $this->getSellerShiprate();

                if ($postData['id']) {
                    $sellerShiprate->addData($postData)->setShiptableratesId($postData['id']);
                } else {
                    $sellerShiprate->setData($postData);
                }

                $sellerShiprate->save();
                $this->_helper->clearCache();
                $this->messageManager->addSuccess(__('Free shipping threshold has been saved successfully.'));

            } catch (\Exception $e) {
                $this->messageManager->addError(__('Something went wrong while saving the threshold.'));
            }
        }

        return $this->resultRedirectFactory->create()->setPath(
            'coditron_customshippingrate/shiptablerates/managewithTabs',
            ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()]
        );
    }
}
