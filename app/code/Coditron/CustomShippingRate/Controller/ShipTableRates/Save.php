<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Category Save action
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        if ($this->getRequest()->isPost()) {
            try {
                $postData = $this->getRequest()->getPostValue();
                $isThreshold = isset($postData['type']) && $postData['type'] === 'threshold';

                // Determine redirect paths based on type
                $manageUrl = $isThreshold ?
                    'coditron_customshippingrate/shiptablerates/managewithTabs' :
                    'coditron_customshippingrate/shiptablerates/manage';
                $manageParams = $isThreshold ?
                    ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()] :
                    ['_secure' => $this->getRequest()->isSecure()];

                if (!$this->_formKeyValidator->validate($this->getRequest())) {
                    return $this->resultRedirectFactory->create()->setPath($manageUrl, $manageParams);
                }

                $postData['seller_id'] = $this->getSellerId();

                // Special validation for thresholds
                if ($isThreshold) {
                    if (!isset($postData['min_amount']) || (float)$postData['min_amount'] <= 0) {
                        $this->messageManager->addError(__('Minimum amount must be greater than 0 for free shipping thresholds.'));
                        return $this->resultRedirectFactory->create()->setPath($manageUrl, $manageParams);
                    }
                    // Set free_shipping to true and shipping_price to 0 for thresholds
                    $postData['free_shipping'] = true;
                    $postData['shipping_price'] = '0.00';
                } else {
                    // For regular shipping methods, ensure min_amount is 0 if not set
                    if (!isset($postData['min_amount']) || empty($postData['min_amount'])) {
                        $postData['min_amount'] = 0;
                    }
                }

                $result = $this->_helper->validateData($postData);
                if ($result['error']) {
                    $this->messageManager->addError(__($result['msg']));
                    return $this->resultRedirectFactory->create()->setPath($manageUrl, $manageParams);
                }

                $sellerShiprate = $this->getSellerShiprate();

                if ($postData['id']) {
                    $sellerShiprate->addData($postData)->setShiptableratesId($postData['id']);
                } else {
                    $sellerShiprate->setData($postData);
                }

                $sellerShiprate->save();
                $id = $sellerShiprate->getShiptableratesId();

                $successMessage = $isThreshold ?
                    __("Free shipping threshold saved successfully.") :
                    __("Shipping Rate saved successfully.");
                $this->messageManager->addSuccess($successMessage);
                $this->_helper->clearCache();

                // Redirect back to the appropriate management page
                return $this->resultRedirectFactory->create()->setPath($manageUrl, $manageParams);

            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());
                $manageUrl = $isThreshold ?? false ?
                    'coditron_customshippingrate/shiptablerates/managewithTabs' :
                    'coditron_customshippingrate/shiptablerates/manage';
                $manageParams = $isThreshold ?? false ?
                    ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()] :
                    ['_secure' => $this->getRequest()->isSecure()];
                return $this->resultRedirectFactory->create()->setPath($manageUrl, $manageParams);
            }
        } else {
            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }
}
