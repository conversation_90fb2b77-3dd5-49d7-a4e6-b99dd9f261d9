<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;

class ManageWithTabs extends \Webkul\MpSellerCategory\Controller\AbstractCategory implements HttpGetActionInterface
{
    /**
     * Execute Method
     *
     * @return \Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $resultPage = $this->_resultPageFactory->create();
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('sellership_layout2_rates_manage_tabs');
        }

        $resultPage->getConfig()->getTitle()->set(__('Manage Shipping Methods'));
        return $resultPage;
    }
}
