<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\App\Action\Context;
use Magento\Ui\Component\MassAction\Filter;
use Coditron\CustomShippingRate\Helper\Data as HelperData;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;

class Delete extends \Magento\Framework\App\Action\Action
{
    /**
     * @var Filter
     */
    protected $_filter;

    /**
     * @var HelperData
     */
    protected $_helper;

    /**
     * @var CollectionFactory
     */
    protected $_collectionFactory;

    /**
     * @param Context $context
     * @param Filter $filter
     * @param HelperData $helper
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        Context $context,
        Filter $filter,
        HelperData $helper,
        CollectionFactory $collectionFactory
    ) {
        $this->_filter = $filter;
        $this->_helper = $helper;
        $this->_collectionFactory = $collectionFactory;
        parent::__construct($context);
    }

    /**
     * Execute action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        try {
            $shiprateId = $this->getRequest()->getParam('shiptablerates_id');
            $isThreshold = $this->getRequest()->getParam('type') === 'threshold';

            // Determine redirect paths based on type
            $manageUrl = $isThreshold ?
                'coditron_customshippingrate/shiptablerates/managewithTabs' :
                'coditron_customshippingrate/shiptablerates/manage';
            $manageParams = $isThreshold ?
                ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()] :
                ['_secure' => $this->getRequest()->isSecure()];

            $sellerShiprateCollection = $this->_collectionFactory->create();
            if ($shiprateId) {
                $collection = $sellerShiprateCollection->addFieldToFilter('shiptablerates_id', ['in' => $shiprateId])
                                                      ->addFieldToFilter('seller_id', $this->getSellerId());

                // Additional filter for thresholds to ensure we only delete threshold records
                if ($isThreshold) {
                    $collection->addFieldToFilter('min_amount', ['gt' => 0]);
                }
            } else {
                $collection = $this->_filter->getCollection($sellerShiprateCollection);
                $collection->addFieldToFilter('seller_id', $this->getSellerId());

                // Additional filter for thresholds
                if ($isThreshold) {
                    $collection->addFieldToFilter('min_amount', ['gt' => 0]);
                }
            }

            $countRecord = $collection->getSize();
            if ($countRecord > 0) {
                $collection->removeShiprates();
                $this->_helper->clearCache();

                $successMessage = $isThreshold ?
                    __('A total of %1 free shipping threshold(s) have been deleted.', $countRecord) :
                    __('A total of %1 record(s) have been deleted.', $countRecord);
                $this->messageManager->addSuccess($successMessage);
            } else {
                $errorMessage = $isThreshold ?
                    __('No valid threshold found to delete.') :
                    __('No valid record found to delete.');
                $this->messageManager->addError($errorMessage);
            }

            return $this->resultRedirectFactory->create()->setPath($manageUrl, $manageParams);
        } catch (\Exception $e) {
            $this->messageManager->addError($e->getMessage());

            $isThreshold = $this->getRequest()->getParam('type') === 'threshold';
            $manageUrl = $isThreshold ?
                'coditron_customshippingrate/shiptablerates/managewithTabs' :
                'coditron_customshippingrate/shiptablerates/manage';
            $manageParams = $isThreshold ?
                ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()] :
                ['_secure' => $this->getRequest()->isSecure()];

            return $this->resultRedirectFactory->create()->setPath($manageUrl, $manageParams);
        }
    }
}
