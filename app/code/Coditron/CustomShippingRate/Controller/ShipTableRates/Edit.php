<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Category Edit action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $isThreshold = $this->getRequest()->getParam('type') === 'threshold';
        $resultPage = $this->_resultPageFactory->create();

        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            if ($isThreshold) {
                $resultPage->addHandle('sellership_layout2_free_shipping_threshold_edit');
            } else {
                $resultPage->addHandle('mpsellership_layout2_rate_edit');
            }
        }

        if (!empty($this->getRequest()->getParam("shiptablerates_id"))) {
            $sellerShiprate = $this->getSellerShiprate();
            if (empty($sellerShiprate->getShiptableratesId())) {
                $this->messageManager->addError("Shipping method does not exist");
                $redirectPath = $isThreshold ?
                    'coditron_customshippingrate/shiptablerates/managewithTabs' :
                    'coditron_customshippingrate/shiptablerates/manage';
                $redirectParams = $isThreshold ?
                    ['tab' => 'free_shipping_thresholds', '_secure' => $this->getRequest()->isSecure()] :
                    ['_secure' => $this->getRequest()->isSecure()];

                return $this->resultRedirectFactory->create()->setPath($redirectPath, $redirectParams);
            }

            $title = $isThreshold ?
                __('Edit Free Shipping Threshold: %1', $sellerShiprate->getCourierName()) :
                $sellerShiprate->getCourierName();
        } else {
            $title = $isThreshold ? __("New Free Shipping Threshold") : __("New Shipping Method");
        }

        $resultPage->getConfig()->getTitle()->set($title);
        return $resultPage;
    }
}
