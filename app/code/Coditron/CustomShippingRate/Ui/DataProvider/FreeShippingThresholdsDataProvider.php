<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Ui\DataProvider;

use Magento\Framework\Api\Filter;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Magento\Customer\Model\Session as CustomerSession;

class FreeShippingThresholdsDataProvider extends AbstractDataProvider
{
    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param CustomerSession $customerSession
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        CustomerSession $customerSession,
        array $meta = [],
        array $data = []
    ) {
        $this->customerSession = $customerSession;
        $this->collection = $collectionFactory->create();
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }

        $sellerId = $this->customerSession->getCustomerId();
        
        // Filter collection to show only records with min_amount > 0 (free shipping thresholds)
        $this->collection->addFieldToFilter('seller_id', $sellerId)
                        ->addFieldToFilter('min_amount', ['gt' => 0]);

        $items = $this->collection->getItems();
        $this->loadedData = [];
        
        foreach ($items as $item) {
            $this->loadedData[$item->getId()] = $item->getData();
        }

        return $this->loadedData;
    }

    /**
     * Add filter
     *
     * @param Filter $filter
     * @return void
     */
    public function addFilter(Filter $filter)
    {
        if ($filter->getField() !== 'fulltext') {
            $this->collection->addFieldToFilter(
                $filter->getField(),
                [$filter->getConditionType() => $filter->getValue()]
            );
        }
    }
}
