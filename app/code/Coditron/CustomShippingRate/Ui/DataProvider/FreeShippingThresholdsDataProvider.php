<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Ui\DataProvider;

use Magento\Framework\Session\SessionManagerInterface;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Webkul\Marketplace\Helper\Data as HelperData;

class FreeShippingThresholdsDataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * @var \Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Collection
     */
    protected $collection;

    /**
     * @var SessionManagerInterface
     */
    protected $session;

    /**
     * @var \Coditron\CustomShippingRate\Helper\Data
     */
    protected $helper;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param \Coditron\CustomShippingRate\Helper\Data $helper
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        \Coditron\CustomShippingRate\Helper\Data $helper,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->collection = $collectionFactory->create();
        $this->helper = $helper;

        // Filter collection to show only records for current seller
        $this->collection->addFieldToFilter('seller_id', ['eq' => $this->helper->getSellerId()]);

        // For now, just show free shipping records until min_amount column is added
        $this->collection->addFieldToFilter('free_shipping', ['eq' => 1]);
    }
}
