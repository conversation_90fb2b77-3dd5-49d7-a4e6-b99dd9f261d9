<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

class ShippingTabs extends Template
{
    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @param Context $context
     * @param MarketplaceHelper $marketplaceHelper
     * @param array $data
     */
    public function __construct(
        Context $context,
        MarketplaceHelper $marketplaceHelper,
        array $data = []
    ) {
        $this->marketplaceHelper = $marketplaceHelper;
        parent::__construct($context, $data);
    }

    /**
     * Get current tab from request
     *
     * @return string
     */
    public function getCurrentTab(): string
    {
        return $this->getRequest()->getParam('tab', 'shipping_methods');
    }

    /**
     * Get tab URL
     *
     * @param string $tab
     * @return string
     */
    public function getTabUrl(string $tab): string
    {
        return $this->getUrl('coditron_customshippingrate/shiptablerates/managewithTabs', ['tab' => $tab]);
    }

    /**
     * Check if tab is active
     *
     * @param string $tab
     * @return bool
     */
    public function isTabActive(string $tab): bool
    {
        return $this->getCurrentTab() === $tab;
    }

    /**
     * Get shipping methods tab content
     *
     * @return string
     */
    public function getShippingMethodsTabContent(): string
    {
        return $this->getChildHtml('shipping_methods_tab');
    }

    /**
     * Get free shipping thresholds tab content
     *
     * @return string
     */
    public function getFreeShippingThresholdsTabContent(): string
    {
        return $this->getChildHtml('free_shipping_thresholds_tab');
    }
}
