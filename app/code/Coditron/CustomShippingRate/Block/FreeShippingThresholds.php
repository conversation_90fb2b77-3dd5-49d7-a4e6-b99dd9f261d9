<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Magento\Customer\Model\Session as CustomerSession;

class FreeShippingThresholds extends Template
{
    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @param Context $context
     * @param MarketplaceHelper $marketplaceHelper
     * @param CollectionFactory $collectionFactory
     * @param CustomerSession $customerSession
     * @param array $data
     */
    public function __construct(
        Context $context,
        MarketplaceHelper $marketplaceHelper,
        CollectionFactory $collectionFactory,
        CustomerSession $customerSession,
        array $data = []
    ) {
        $this->marketplaceHelper = $marketplaceHelper;
        $this->collectionFactory = $collectionFactory;
        $this->customerSession = $customerSession;
        parent::__construct($context, $data);
    }

    /**
     * Get seller ID
     *
     * @return int
     */
    public function getSellerId(): int
    {
        return (int)$this->customerSession->getCustomerId();
    }

    /**
     * Get add new threshold URL
     *
     * @return string
     */
    public function getAddNewThresholdUrl(): string
    {
        return $this->getUrl('coditron_customshippingrate/freeshippingthreshold/new');
    }

    /**
     * Get edit threshold URL
     *
     * @param int $thresholdId
     * @return string
     */
    public function getEditThresholdUrl(int $thresholdId): string
    {
        return $this->getUrl('coditron_customshippingrate/freeshippingthreshold/edit', ['id' => $thresholdId]);
    }

    /**
     * Get delete threshold URL
     *
     * @param int $thresholdId
     * @return string
     */
    public function getDeleteThresholdUrl(int $thresholdId): string
    {
        return $this->getUrl('coditron_customshippingrate/freeshippingthreshold/delete', ['id' => $thresholdId]);
    }
}
