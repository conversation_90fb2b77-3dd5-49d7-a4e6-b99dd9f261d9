<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Coditron\CustomShippingRate\Model\ShipTableRatesFactory;
use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Directory\Block\Data as DirectoryBlock;
use Magento\Framework\Json\Helper\Data as JsonHelper;

class FreeShippingThresholdEdit extends Template
{
    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @var ShipTableRatesFactory
     */
    protected $shipTableRatesFactory;

    /**
     * @var ShipTableRatesRepositoryInterface
     */
    protected $shipTableRatesRepository;

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var DirectoryBlock
     */
    protected $directoryBlock;

    /**
     * @var JsonHelper
     */
    protected $jsonHelper;

    /**
     * @param Context $context
     * @param MarketplaceHelper $marketplaceHelper
     * @param ShipTableRatesFactory $shipTableRatesFactory
     * @param ShipTableRatesRepositoryInterface $shipTableRatesRepository
     * @param CustomerSession $customerSession
     * @param DirectoryBlock $directoryBlock
     * @param JsonHelper $jsonHelper
     * @param array $data
     */
    public function __construct(
        Context $context,
        MarketplaceHelper $marketplaceHelper,
        ShipTableRatesFactory $shipTableRatesFactory,
        ShipTableRatesRepositoryInterface $shipTableRatesRepository,
        CustomerSession $customerSession,
        DirectoryBlock $directoryBlock,
        JsonHelper $jsonHelper,
        array $data = []
    ) {
        $this->marketplaceHelper = $marketplaceHelper;
        $this->shipTableRatesFactory = $shipTableRatesFactory;
        $this->shipTableRatesRepository = $shipTableRatesRepository;
        $this->customerSession = $customerSession;
        $this->directoryBlock = $directoryBlock;
        $this->jsonHelper = $jsonHelper;
        parent::__construct($context, $data);
    }

    /**
     * Get threshold data
     *
     * @return \Coditron\CustomShippingRate\Model\ShipTableRates
     */
    public function getThresholdData()
    {
        $thresholdId = $this->getRequest()->getParam('id');
        if ($thresholdId) {
            try {
                return $this->shipTableRatesRepository->getById($thresholdId);
            } catch (\Exception $e) {
                return $this->shipTableRatesFactory->create();
            }
        }
        return $this->shipTableRatesFactory->create();
    }

    /**
     * Get save URL
     *
     * @return string
     */
    public function getSaveUrl(): string
    {
        return $this->getUrl('coditron_customshippingrate/freeshippingthreshold/save');
    }

    /**
     * Get back URL
     *
     * @return string
     */
    public function getBackUrl(): string
    {
        return $this->getUrl('coditron_customshippingrate/shiptablerates/managewithTabs', ['tab' => 'free_shipping_thresholds']);
    }

    /**
     * Get countries options
     *
     * @return array
     */
    public function getCountriesOptions(): array
    {
        return $this->directoryBlock->getCountryHtmlSelect();
    }

    /**
     * Get seller ID
     *
     * @return int
     */
    public function getSellerId(): int
    {
        return (int)$this->customerSession->getCustomerId();
    }

    /**
     * Check if this is edit mode
     *
     * @return bool
     */
    public function isEditMode(): bool
    {
        return (bool)$this->getRequest()->getParam('id');
    }
}
