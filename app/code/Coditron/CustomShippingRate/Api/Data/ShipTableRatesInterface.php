<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Api\Data;

interface ShipTableRatesInterface
{
    const SHIPTABLERATES_ID = 'shiptablerates_id';

    const SELLER_ID = 'seller_id';
    const COURIER_NAME = 'courier_name';
    const SERVICE_TYPE = 'service_type';
    const COUNTRIES = 'countries';
    const RETURN_ADDRESS_ID = 'return_address_id';
    const PACKING_TIME = 'packing_time';
    const DELIVERY_TIME = 'delivery_time';
    const TOTAL_LEAD_TIME = 'total_lead_time';
    const WEIGHT = 'weight';
    const SHIPPING_PRICE = 'shipping_price';
    const FREE_SHIPPING = 'free_shipping';
    const MIN_AMOUNT = 'min_amount';

    /**
     * Get shiptablerates_id
     * @return int|null
     */
    public function getShiptableratesId(): ?int;

    /**
     * Set shiptablerates_id
     * @param int $shiptableratesId
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setShiptableratesId(int $shiptableratesId): ShipTableRatesInterface;

    /**
     * Get seller_id
     * @return int|null
     */
    public function getSellerId(): ?int;

    /**
     * Set seller_id
     * @param int $sellerId
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setSellerId(int $sellerId): ShipTableRatesInterface;

    /**
     * Get courier
     * @return string|null
     */
    public function getCourier(): ?string;

    /**
     * Set courier
     * @param string $courier
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setCourier(string $courier): ShipTableRatesInterface;

    /**
     * Get service type
     * @return string|null
     */
    public function getServiceType(): ?string;

    /**
     * Set service type
     * @param string $serviceType
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setServiceType(string $serviceType): ShipTableRatesInterface;

    /**
     * Get countries
     * @return array
     */
    public function getCountries(): array;

    /**
     * Set countries
     * @param array $countries
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setCountries(array $countries): ShipTableRatesInterface;

    /**
     * Get return address id
     * @return int|null
     */
    public function getReturnAddressId(): ?int;

    /**
     * Set return address id
     * @param int $addressId
     * @return ShipTableRatesInterface
     */
    public function setReturnAddressId(int $addressId): ShipTableRatesInterface;

    /**
     * Get weight
     * @return int|null
     */
    public function getPackingTime(): ?int;

    /**
     * Set packing time
     * @param int $delay
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setPackingTime(int $delay): ShipTableRatesInterface;

    /**
     * Get weight
     * @return int|null
     */
    public function getDeliveryTime(): ?int;

    /**
     * Set delivery time
     * @param int $delay
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setDeliveryTime(int $delay): ShipTableRatesInterface;

    /**
     * Get total lead time
     * @return int|null
     */
    public function getTotalLeadTime(): ?int;

    /**
     * Set total lead time
     * @param int $delay
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setTotalLeadTime(int $delay): ShipTableRatesInterface;

    /**
     * Get weight
     * @return string|null
     */
    public function getWeight(): ?string;

    /**
     * Set weight
     * @param string $weight
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setWeight(string $weight): ShipTableRatesInterface;

    /**
     * Get shipping_price
     * @return string|null
     */
    public function getShippingPrice(): ?string;

    /**
     * Set shipping_price
     * @param string $shippingPrice
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setShippingPrice(string $shippingPrice): ShipTableRatesInterface;

    /**
     * Get free shipping
     * @return bool
     */
    public function getFreeShipping(): bool;

    /**
     * Set free shipping
     * @param bool $freeShipping
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setFreeShipping(bool $freeShipping): ShipTableRatesInterface;

    /**
     * Get minimum amount for free shipping
     * @return float|null
     */
    public function getMinAmount(): ?float;

    /**
     * Set minimum amount for free shipping
     * @param float $minAmount
     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
     */
    public function setMinAmount(float $minAmount): ShipTableRatesInterface;

}

